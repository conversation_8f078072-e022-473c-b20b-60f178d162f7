<template>
  <DefaultLayout>
    <div class="glass-morphic-bg main-homepage-container">
      <!-- 彩色光斑背景效果 -->
      <div class="glass-orb glass-orb-1"></div>
      <div class="glass-orb glass-orb-2"></div>
      <div class="glass-orb glass-orb-3"></div>
      <div class="glass-orb glass-orb-4"></div>
      
      <!-- 主标题区域 -->
      <section class="hero-section">
        <div class="container">
          <div class="hero-content glass-card">
            <h1 class="main-title">HiProf 智能教育平台</h1>
            <p class="main-subtitle">
              集知识图谱、AI助手、备课工具于一体的综合教育解决方案
            </p>
          </div>
        </div>
      </section>

      <!-- 三大模块区域 -->
      <section class="modules-section">
        <div class="container">
          <h2 class="section-title">选择您需要的功能模块</h2>
          
          <div class="modules-grid">
            <!-- 知识图谱模块 -->
            <div class="module-card glass-card" @click="navigateToKnowledgeGraph">
              <div class="module-icon" :style="{ backgroundColor: 'rgba(59, 130, 246, 0.7)' }">
                <span class="icon-placeholder">🔍</span>
              </div>
              <h3 class="module-title">知识图谱</h3>
              <h4 class="module-subtitle">Knowledge Graph</h4>
              <p class="module-description">
                通过可视化图谱直观展示知识点之间的关联关系，构建完整的知识体系，
                支持交互式探索和个性化学习路径推荐。
              </p>
              <div class="module-features">
                <span class="feature-tag">可视化展示</span>
                <span class="feature-tag">关联分析</span>
                <span class="feature-tag">学习路径</span>
              </div>
              <button class="module-button glass-button">
                进入知识图谱 →
              </button>
            </div>

            <!-- AI智能助手模块 -->
            <div class="module-card glass-card" @click="goToAIAssistant">
              <div class="module-icon" :style="{ backgroundColor: 'rgba(139, 92, 246, 0.7)' }">
                <span class="icon-placeholder">🤖</span>
              </div>
              <h3 class="module-title">AI智能助手</h3>
              <h4 class="module-subtitle">AI Intelligent Assistant</h4>
              <p class="module-description">
                基于先进AI技术的智能学习助手，提供个性化问答、学习建议、
                内容生成等功能，让学习更高效、更智能。
              </p>
              <div class="module-features">
                <span class="feature-tag">智能问答</span>
                <span class="feature-tag">学习建议</span>
                <span class="feature-tag">内容生成</span>
              </div>
              <button class="module-button glass-button">
                进入AI助手 →
              </button>
            </div>

            <!-- 教师备课助手模块 - 仅教师可见 -->
            <div v-if="userRole === 'teacher'" class="module-card glass-card" @click="goToTeacherPrep">
              <div class="module-icon" :style="{ backgroundColor: 'rgba(16, 185, 129, 0.7)' }">
                <span class="icon-placeholder">📚</span>
              </div>
              <h3 class="module-title">教师备课助手</h3>
              <h4 class="module-subtitle">Teacher Lesson Preparation Assistant</h4>
              <p class="module-description">
                专为教师设计的备课工具，提供教案模板、课程设计、
                教学资源管理等功能，提升备课效率和教学质量。
              </p>
              <div class="module-features">
                <span class="feature-tag">教案模板</span>
                <span class="feature-tag">课程设计</span>
                <span class="feature-tag">资源管理</span>
              </div>
              <button class="module-button glass-button">
                进入备课 →
              </button>
            </div>

            <!-- 学生学习中心模块 - 仅学生可见 -->
            <div v-if="userRole === 'student'" class="module-card glass-card" @click="goToStudentCenter">
              <div class="module-icon" :style="{ backgroundColor: 'rgba(16, 185, 129, 0.7)' }">
                <span class="icon-placeholder">🎓</span>
              </div>
              <h3 class="module-title">学生学习中心</h3>
              <h4 class="module-subtitle">Student Learning Center</h4>
              <p class="module-description">
                专为学生设计的学习平台，提供课程学习、进度跟踪、
                学习资料管理等功能，提升学习效率和学习体验。
              </p>
              <div class="module-features">
                <span class="feature-tag">课程学习</span>
                <span class="feature-tag">进度跟踪</span>
                <span class="feature-tag">学习资料</span>
              </div>
              <button class="module-button glass-button">
                进入学习 →
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </DefaultLayout>
</template>

<script setup>
import { onMounted, nextTick, computed } from 'vue';
import { useRouter } from 'vue-router';
import DefaultLayout from '@/layouts/DefaultLayout.vue';
import { getCurrentUser } from '@/api/auth';

const router = useRouter();

// 获取用户角色
const userRole = computed(() => {
  const user = getCurrentUser();
  return user?.role || null;
});

// 导航到知识图谱页面
const navigateToKnowledgeGraph = () => {
  router.push('/knowledge-graph');
};

// 显示即将推出提示
const showComingSoon = (moduleName) => {
  alert(`${moduleName}功能正在开发中，敬请期待！`);
};

// 跳转到AI智能助手
const goToAIAssistant = () => {
  router.push('/ai-assistant');
};

// 跳转到教师备课助手
const goToTeacherPrep = () => {
  router.push('/teacher');
};

// 跳转到学生学习中心
const goToStudentCenter = () => {
  router.push('/student');
};

// 处理页面加载
onMounted(() => {
  nextTick(() => {
    window.scrollTo({ top: 0, behavior: 'instant' });
  });
});
</script>

<style scoped>
@import '../assets/styles/glassmorphism.css';

.main-homepage-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(224, 242, 254, 0.8) 0%, 
    rgba(240, 253, 250, 0.8) 25%,
    rgba(254, 249, 195, 0.8) 50%,
    rgba(250, 232, 255, 0.8) 75%,
    rgba(239, 246, 255, 0.8) 100%);
  position: relative;
  backdrop-filter: blur(var(--glass-blur, 16px));
  -webkit-backdrop-filter: blur(var(--glass-blur, 16px));
}

/* 添加微妙的网格背景 */
.main-homepage-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: 1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

/* 主标题区域 */
.hero-section {
  padding: var(--spacing-xl) 0;
  text-align: center;
}

.hero-content {
  padding: var(--spacing-xl);
  max-width: 800px;
  margin: 0 auto;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: rgba(0, 0, 0, 0.9);
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.5);
  background: linear-gradient(135deg, #1e40af, #7c3aed, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.main-subtitle {
  font-size: 1.25rem;
  color: rgba(0, 0, 0, 0.7);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* 模块区域 */
.modules-section {
  padding: var(--spacing-xl) 0 var(--spacing-xxl);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: var(--spacing-xl);
  color: rgba(0, 0, 0, 0.85);
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.5);
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.module-card {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-xl);
  height: 100%;
  cursor: pointer;
  transition: var(--glass-transition);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--glass-shadow-hover);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.25);
}

.module-icon {
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.icon-placeholder {
  font-size: 40px;
}

.module-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

.module-subtitle {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: var(--spacing-md);
  color: rgba(0, 0, 0, 0.6);
  text-align: center;
  font-style: italic;
}

.module-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.7);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  flex-grow: 1;
}

.module-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.feature-tag {
  font-size: 0.875rem;
  padding: 4px 12px;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.module-button {
  font-size: 1.1rem;
  padding: 14px 28px;
  font-weight: 600;
  border-radius: 12px;
  margin-top: auto;
  width: 100%;
  text-align: center;
}

.module-button.coming-soon {
  background: rgba(156, 163, 175, 0.3);
  color: rgba(0, 0, 0, 0.6);
  cursor: not-allowed;
}

.module-button.coming-soon:hover {
  transform: none;
  background: rgba(156, 163, 175, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .module-card {
    padding: var(--spacing-lg);
  }
  
  .container {
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }
  
  .main-subtitle {
    font-size: 1.1rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
}
</style>
