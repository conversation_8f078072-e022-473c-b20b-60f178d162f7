<template>
  <div class="my-page">
    <div class="container py-3 full-width">
      <div class="page-header">
        <router-link to="/" class="back-to-home-btn">
          <i class="back-icon"></i>
          <span>返回首页</span>
        </router-link>
        <h1 class="page-title">个人中心</h1>
      </div>
      
      <!-- 左侧菜单和主内容区 -->
      <div class="content-layout">
        <!-- 左侧菜单 -->
        <div class="sidebar">
          <div class="sidebar-menu">
            <!-- 按照新顺序排列菜单项 -->
            <div 
              class="menu-item" 
              :class="{ active: activeTab === 'dashboard' }" 
              @click="activeTab = 'dashboard'"
            >
              <i class="menu-icon dashboard-icon"></i>
              <span class="menu-text">概览</span>
            </div>
            <div 
              class="menu-item" 
              :class="{ active: activeTab === 'outline' }" 
              @click="activeTab = 'outline'"
            >
              <i class="menu-icon outline-icon"></i>
              <span class="menu-text">知识大纲</span>
            </div>
            <div 
              class="menu-item" 
              :class="{ active: activeTab === 'graphview' }" 
              @click="activeTab = 'graphview'"
            >
              <i class="menu-icon graphview-icon"></i>
              <span class="menu-text">知识图谱列表</span>
            </div>
            <div 
              class="menu-item" 
              :class="{ active: activeTab === 'graphs' }" 
              @click="activeTab = 'graphs'"
            >
              <i class="menu-icon graphs-icon"></i>
              <span class="menu-text">{{ isTeacher ? '我的知识图谱' : '学习的知识图谱' }}</span>
            </div>
            <div 
              class="menu-item" 
              :class="{ active: activeTab === 'history' }" 
              @click="activeTab = 'history'"
            >
              <i class="menu-icon history-icon"></i>
              <span class="menu-text">{{ isTeacher ? '编辑历史' : '学习历史' }}</span>
            </div>
            <div 
              class="menu-item" 
              :class="{ active: activeTab === 'settings' }" 
              @click="activeTab = 'settings'"
            >
              <i class="menu-icon settings-icon"></i>
              <span class="menu-text">设置</span>
            </div>
          </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
          <!-- 个人信息面板 - 已删除 -->
          
          <!-- 概览面板 -->
          <div v-if="activeTab === 'dashboard'" class="dashboard-panel">
            <MyDashboard :is-teacher="isTeacher" />
          </div>
          
          <!-- 知识图谱列表面板 -->
          <div v-if="activeTab === 'graphs'" class="graphs-panel">
            <MyGraphs :is-teacher="isTeacher" />
          </div>
          
          <!-- 知识大纲面板 -->
          <div v-if="activeTab === 'outline'" class="outline-panel">
            <div v-if="selectedOutline" class="selected-outline-container">
              <div class="outline-header">
                <button class="back-button" @click="selectedOutline = null">
                  <i class="back-icon"></i>
                  <span>返回大纲列表</span>
                </button>
                <h3 class="outline-title">{{ selectedOutline.title }}</h3>
              </div>
              <iframe :src="`/outline?embedded=true&onlyOutline=true&courseId=${selectedOutline.id}`" class="embedded-view"></iframe>
            </div>
            <MyOutlineList v-else @select-outline="handleOutlineSelect" />
          </div>
          
          <!-- 知识图谱视图面板 -->
          <div v-if="activeTab === 'graphview'" class="graphview-panel">
            <div v-if="selectedGraph" class="selected-graph-container">
              <div class="graph-header">
                <button class="back-button" @click="selectedGraph = null">
                  <i class="back-icon"></i>
                  <span>返回图谱列表</span>
                </button>
                <h3 class="graph-title">{{ selectedGraph.title }}</h3>
              </div>
              <iframe :src="`/graph?embedded=true&courseId=${selectedGraph.id}`" class="embedded-view"></iframe>
            </div>
            <MyGraphList v-else @select-graph="handleGraphSelect" />
          </div>
          
          <!-- 历史记录面板 -->
          <div v-if="activeTab === 'history'" class="history-panel">
            <MyHistory :is-teacher="isTeacher" />
          </div>
          
          <!-- 设置面板 -->
          <div v-if="activeTab === 'settings'" class="settings-panel">
            <MySettings />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getCurrentUser } from '@/api/auth';
import MyDashboard from '@/components/my/MyDashboard.vue';
import MyGraphs from '@/components/my/MyGraphs.vue';
import MyHistory from '@/components/my/MyHistory.vue';
import MySettings from '@/components/my/MySettings.vue';
// 移除个人信息组件导入
import MyOutlineList from '@/components/my/MyOutlineList.vue';
import MyGraphList from '@/components/my/MyGraphList.vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 当前用户信息
const currentUser = ref(getCurrentUser() || {});

// 是否为教师角色
const isTeacher = computed(() => {
  return currentUser.value.role === 'teacher';
});

// 当前激活的标签页 - 默认改为概览
const activeTab = ref('dashboard');

// 组件挂载时加载用户数据
onMounted(async () => {
  // TODO: 从API获取最新的用户数据
  console.log('加载用户数据');
  
  // 检查URL参数中是否指定了要显示的标签页
  if (route.query.tab) {
    activeTab.value = route.query.tab;
  }
});

const selectedOutline = ref(null);
const selectedGraph = ref(null);

const handleOutlineSelect = (outline) => {
  selectedOutline.value = outline;
};

const handleGraphSelect = (graph) => {
  selectedGraph.value = graph;
};
</script>

<style scoped>
.my-page {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
}

.full-width {
  max-width: 100% !important;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-top: 0.25rem;
}

.back-to-home-btn {
  display: flex;
  align-items: center;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  margin-right: 1rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
}

.back-to-home-btn:hover {
  background-color: var(--hover-color);
}

.back-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  position: relative;
}

.back-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 12px;
  height: 12px;
  border-left: 2px solid var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  transform: translateY(-50%) rotate(45deg);
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

/* 新增的左侧菜单和内容布局样式 */
.content-layout {
  display: flex;
  gap: 0;
  min-height: 600px;
  height: calc(100vh - 60px);
}

.sidebar {
  width: 180px;
  flex-shrink: 0;
  background-color: #f5f5f5;
  border-radius: 0;
  box-shadow: none;
  padding: 0.5rem 0;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: none;
  margin-bottom: 4px;
  border-radius: 0;
}

.menu-item:hover {
  background-color: #e9e9e9;
}

.menu-item.active {
  background-color: #e9e9e9;
  color: var(--primary-color);
  font-weight: 500;
  border-left: 4px solid var(--primary-color);
}

.menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.7;
}

.menu-item.active .menu-icon {
  opacity: 1;
}

.menu-text {
  font-size: 0.9375rem;
  font-weight: 400;
}

.main-content {
  flex: 1;
  background-color: #ffffff;
  border-radius: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 1rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dashboard-panel, .graphs-panel, .outline-panel, .graphview-panel, .history-panel, .settings-panel {
  flex: 1;
  overflow: auto;
  height: 100%;
  scrollbar-width: thin;
  scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
}

.dashboard-panel::-webkit-scrollbar,
.graphs-panel::-webkit-scrollbar,
.outline-panel::-webkit-scrollbar,
.graphview-panel::-webkit-scrollbar,
.history-panel::-webkit-scrollbar,
.settings-panel::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.dashboard-panel::-webkit-scrollbar-track,
.graphs-panel::-webkit-scrollbar-track,
.outline-panel::-webkit-scrollbar-track,
.graphview-panel::-webkit-scrollbar-track,
.history-panel::-webkit-scrollbar-track,
.settings-panel::-webkit-scrollbar-track {
  background: transparent;
}

.dashboard-panel::-webkit-scrollbar-thumb,
.graphs-panel::-webkit-scrollbar-thumb,
.outline-panel::-webkit-scrollbar-thumb,
.graphview-panel::-webkit-scrollbar-thumb,
.history-panel::-webkit-scrollbar-thumb,
.settings-panel::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.3);
  border-radius: 4px;
}

.dashboard-panel::-webkit-scrollbar-thumb:hover,
.graphs-panel::-webkit-scrollbar-thumb:hover,
.outline-panel::-webkit-scrollbar-thumb:hover,
.graphview-panel::-webkit-scrollbar-thumb:hover,
.history-panel::-webkit-scrollbar-thumb:hover,
.settings-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(150, 150, 150, 0.5);
}

/* 嵌入式视图 */
.embedded-view {
  width: 100%;
  height: 95vh;
  min-height: 500px;
  border: none;
  border-radius: var(--border-radius-md);
}

.py-3 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

/* 菜单图标 */
.dashboard-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}

.graphs-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
}

.outline-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z'/%3E%3C/svg%3E");
}

.graphview-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.5 5.5v13h-15v-13h15m0-2h-15c-1.1 0-2 .9-2 2v13c0 1.1.9 2 2 2h15c1.1 0 2-.9 2-2v-13c0-1.1-.9-2-2-2zM9 8h2v8H9zm4 3h2v5h-2zm-8 0h2v5H5z'/%3E%3C/svg%3E");
}

.history-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z'/%3E%3C/svg%3E");
}

.settings-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
}

.sidebar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.3);
  border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(150, 150, 150, 0.5);
}

@media (max-width: 768px) {
  .content-layout {
    flex-direction: column;
    height: auto;
  }
  
  .sidebar {
    width: 100%;
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 1200px) {
  .container.full-width {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .content-layout {
    gap: 2rem;
  }
}

@media (min-width: 1600px) {
  .container.full-width {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .sidebar {
    width: 200px;
  }
  
  .content-layout {
    height: calc(100vh - 50px);
  }
  
  .embedded-view {
    height: 98vh;
  }
}

/* 大纲相关样式 */
.selected-outline-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.outline-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background-color: var(--background-color);
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #007AFF;
  font-size: 0.9375rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(0, 122, 255, 0.1);
}

.back-button .back-icon {
  margin-right: 0.375rem;
  width: 16px;
  height: 16px;
  position: relative;
}

.back-button .back-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 10px;
  height: 10px;
  border-left: 2px solid #007AFF;
  border-bottom: 2px solid #007AFF;
  transform: translateY(-50%) rotate(45deg);
}

.outline-title {
  margin: 0 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 500;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .outline-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
}

/* 知识图谱列表相关样式 */
.selected-graph-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.graph-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background-color: var(--background-color);
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #007AFF;
  font-size: 0.9375rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(0, 122, 255, 0.1);
}

.back-button .back-icon {
  margin-right: 0.375rem;
  width: 16px;
  height: 16px;
  position: relative;
}

.back-button .back-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 10px;
  height: 10px;
  border-left: 2px solid #007AFF;
  border-bottom: 2px solid #007AFF;
  transform: translateY(-50%) rotate(45deg);
}

.graph-title {
  margin: 0 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 500;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .graph-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .sidebar {
    background-color: #1e1e1e;
  }
  
  .menu-item:hover {
    background-color: #2a2a2a;
  }
  
  .menu-item.active {
    background-color: #2a2a2a;
  }
  
  .main-content {
    background-color: #121212;
  }
}
</style> 