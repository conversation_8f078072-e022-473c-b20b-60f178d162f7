# HiProf 前端页面重构计划

## 项目概述
将现有前端页面重构为三个主要页面：首页、教师端页面、学生端页面，并将知识图谱体验功能集成到首页中。

## 当前代码结构分析

### 现有页面结构
1. **MainHomePage.vue** - 当前首页，包含三大模块导航
2. **KnowledgeGraphHomePage.vue** - 独立的知识图谱首页
3. **TeacherLayout.vue** - 教师端布局页面
4. **StudentLayout.vue** - 学生端布局页面
5. **GraphPage.vue** - 知识图谱详细页面
6. **MyPage.vue** - 个人中心页面

### 现有功能模块
1. **知识图谱功能**
   - 图谱列表展示 (GraphList.vue)
   - 图谱可视化 (RelationGraph组件)
   - 交互式演示 (InteractiveDemo.vue)
   - 节点编辑和管理
   - 多种布局支持（中心布局、层次布局等）

2. **教师端功能**
   - 教案生成和管理
   - PPT生成
   - 课程管理
   - 学习资料管理

3. **学生端功能**
   - 学习概览
   - 课程学习
   - 学习进度跟踪
   - 知识图谱学习

### 路由配置分析
- `/` - 主首页
- `/knowledge-graph` - 知识图谱首页（需要移除）
- `/teacher/*` - 教师端页面
- `/student/*` - 学生端页面
- `/graph/:id` - 具体图谱页面

## 重构目标

### 1. 新首页设计
**目标**: 整合现有功能，参考首页原型图设计
**主要变更**:
- 保留现有的玻璃态效果和动画
- 添加轮播图组件（参考原型图）
- 在轮播图下方集成知识图谱体验功能
- 保留三大模块导航（知识图谱、AI助手、备课工具）
- 添加平台特色展示区域

### 2. 教师端页面优化
**目标**: 参考教师端原型图，优化布局和功能
**主要变更**:
- 优化侧边栏导航结构
- 改进个人信息展示
- 整合知识图谱管理功能
- 优化教案和PPT生成界面

### 3. 学生端页面优化
**目标**: 参考学生端原型图，提升学习体验
**主要变更**:
- 优化学习仪表板
- 改进课程展示方式
- 集成个性化学习路径
- 优化知识图谱学习界面

## 详细实施计划

### 阶段一：准备工作和分析（1-2天）
**任务列表**:
1. **代码结构深度分析**
   - 详细分析现有组件依赖关系
   - 识别可复用的组件和样式
   - 分析API接口使用情况
   - 评估现有状态管理

2. **功能映射规划**
   - 制定知识图谱功能迁移方案
   - 规划组件复用策略
   - 设计新的路由结构
   - 制定数据流重构方案

3. **原型图分析**
   - 详细分析三个原型图的设计要求
   - 提取关键UI组件和布局
   - 制定样式迁移计划
   - 规划响应式适配方案

### 阶段二：新首页重构（3-4天）
**任务列表**:
1. **轮播图组件开发**
   - 创建新的轮播图组件（参考原型图）
   - 实现自动播放和手动切换
   - 添加导航点和箭头控制
   - 集成教师/学生入口按钮

2. **知识图谱体验区集成**
   - 将InteractiveDemo组件集成到首页
   - 优化知识图谱展示效果
   - 添加"体验知识图谱"功能入口
   - 保持现有的图谱交互功能

3. **首页布局重构**
   - 重新设计首页整体布局
   - 保留玻璃态效果和动画
   - 添加平台特色展示区域
   - 优化移动端响应式布局

4. **导航和路由调整**
   - 移除独立的知识图谱首页路由
   - 调整主导航结构
   - 更新相关链接和跳转逻辑

### 阶段三：教师端页面重构（4-5天）
**任务列表**:
1. **布局组件重构**
   - 参考教师端原型图重构TeacherLayout.vue
   - 优化侧边栏导航设计
   - 改进头部用户信息展示
   - 添加快捷操作入口

2. **知识图谱管理集成**
   - 将图谱创建和管理功能集成到教师端
   - 优化图谱列表展示
   - 添加图谱与课程的关联功能
   - 改进图谱编辑界面

3. **教案和PPT功能优化**
   - 优化教案生成界面
   - 改进PPT生成流程
   - 添加模板选择功能
   - 优化文件管理界面

4. **个人中心整合**
   - 整合MyPage.vue的教师相关功能
   - 优化个人信息管理
   - 添加使用统计和历史记录
   - 改进设置和偏好配置

### 阶段四：学生端页面重构（4-5天）
**任务列表**:
1. **学习仪表板重构**
   - 参考学生端原型图重构StudentDashboardPage.vue
   - 优化学习进度展示
   - 添加个性化推荐区域
   - 改进课程快捷入口

2. **知识图谱学习优化**
   - 优化StudentKnowledgeGraphPage.vue
   - 添加学习路径推荐
   - 改进节点学习状态展示
   - 集成学习进度跟踪

3. **课程学习界面优化**
   - 改进课程列表展示
   - 优化学习资料管理
   - 添加学习笔记功能
   - 改进学习历史记录

4. **个人中心整合**
   - 整合MyPage.vue的学生相关功能
   - 优化学习统计展示
   - 添加成就和徽章系统
   - 改进个人设置界面

### 阶段五：功能测试和优化（2-3天）
**任务列表**:
1. **功能完整性测试**
   - 测试所有现有功能是否正常工作
   - 验证知识图谱功能迁移完整性
   - 测试教师和学生端所有功能
   - 验证路由跳转和权限控制

2. **用户体验优化**
   - 优化页面加载性能
   - 改进动画和过渡效果
   - 优化移动端体验
   - 修复UI细节问题

3. **兼容性测试**
   - 测试不同浏览器兼容性
   - 验证响应式布局效果
   - 测试API接口调用
   - 验证数据持久化

## 技术实施细节

### 组件复用策略
1. **保留的核心组件**
   - RelationGraph（知识图谱核心组件）
   - GraphList.vue（图谱列表）
   - InteractiveDemo.vue（交互演示）
   - 所有common/目录下的基础组件

2. **需要重构的组件**
   - MainHomePage.vue（首页主体）
   - TeacherLayout.vue（教师端布局）
   - StudentLayout.vue（学生端布局）
   - 相关的页面级组件

3. **需要新建的组件**
   - HomeCarousel.vue（首页轮播图）
   - KnowledgeGraphExperience.vue（知识图谱体验区）
   - PlatformFeatures.vue（平台特色展示）

### 路由重构方案
1. **移除的路由**
   - `/knowledge-graph` - 独立知识图谱首页

2. **保留的路由**
   - `/` - 新首页
   - `/teacher/*` - 教师端所有路由
   - `/student/*` - 学生端所有路由
   - `/graph/:id` - 具体图谱页面

3. **新增的路由**
   - `/graph/experience` - 知识图谱体验页面（可选）

### 样式和主题
1. **保留现有样式**
   - 玻璃态效果（glassmorphism.css）
   - 主题色彩系统（theme.css）
   - 全局样式（global.css）

2. **新增样式**
   - 轮播图相关样式
   - 原型图适配样式
   - 响应式优化样式

## 风险评估和应对

### 主要风险
1. **功能丢失风险**
   - 风险：知识图谱功能迁移过程中可能丢失部分功能
   - 应对：详细测试现有功能，制定功能检查清单

2. **用户体验下降风险**
   - 风险：重构后用户操作流程可能变复杂
   - 应对：保持核心操作流程不变，优化细节体验

3. **开发时间超期风险**
   - 风险：重构工作量可能超出预期
   - 应对：分阶段实施，优先保证核心功能

### 质量保证措施
1. **代码审查**
   - 每个阶段完成后进行代码审查
   - 确保代码质量和规范性
   - 验证功能完整性

2. **测试策略**
   - 功能测试：确保所有现有功能正常
   - 兼容性测试：验证多浏览器支持
   - 性能测试：确保页面加载性能

3. **回滚方案**
   - 保留现有代码备份
   - 制定快速回滚策略
   - 准备应急修复方案

## 成功标准

### 功能完整性
- [ ] 所有现有功能在新页面中正常工作
- [ ] 知识图谱体验功能成功集成到首页
- [ ] 教师端和学生端功能完整迁移
- [ ] 路由跳转和权限控制正常

### 用户体验
- [ ] 页面加载速度不低于现有水平
- [ ] 移动端响应式效果良好
- [ ] 操作流程简化且直观
- [ ] 视觉效果符合原型图要求

### 技术质量
- [ ] 代码结构清晰，可维护性良好
- [ ] 组件复用率高，冗余代码少
- [ ] API调用效率优化
- [ ] 错误处理完善

## 后续优化计划

### 短期优化（重构完成后1-2周）
1. 根据用户反馈优化界面细节
2. 性能监控和优化
3. 修复发现的bug和问题
4. 完善文档和注释

### 中期优化（重构完成后1个月）
1. 添加更多个性化功能
2. 优化知识图谱算法
3. 增强AI助手功能
4. 改进数据分析和统计

### 长期规划（重构完成后3个月）
1. 移动端APP开发
2. 更多AI功能集成
3. 社交学习功能
4. 高级分析和报告功能

## 详细技术实施指南

### 知识图谱功能迁移详细步骤

#### 1. 组件迁移清单
**需要迁移的组件**：
- `src/components/home/<USER>
- `src/components/home/<USER>
- `src/components/home/<USER>
- `src/components/graph/GraphList.vue` → 保留并优化
- `src/pages/KnowledgeGraphHomePage.vue` → 功能拆分迁移

**API接口保持不变**：
- `src/api/graph.js` - 所有接口保持现有功能
- 图谱数据结构不变
- 后端接口调用方式不变

#### 2. 首页轮播图实现方案
**参考原型图要求**：
```html
<!-- 轮播图结构 -->
<section class="carousel-container">
  <div class="carousel-wrapper">
    <div class="carousel-slide slide-1">
      <h2>AI赋能OBE智慧教学平台</h2>
      <p>基于成果导向教育理念，融合人工智能技术的新一代教学平台</p>
      <div class="entry-buttons">
        <a href="/teacher" class="btn-teacher">教师入口</a>
        <a href="/student" class="btn-student">学生入口</a>
      </div>
    </div>
    <!-- 更多轮播项 -->
  </div>
</section>
```

**样式要求**：
- 渐变背景效果
- 玻璃态按钮
- 自动轮播（5秒间隔）
- 导航点和箭头控制
- 响应式适配

#### 3. 知识图谱体验区实现
**集成位置**：轮播图下方，平台特色上方
**功能要求**：
- 展示简化版知识图谱
- 提供交互体验
- 引导用户进入完整功能
- 保持现有的图谱渲染效果

**实现代码结构**：
```vue
<template>
  <section class="knowledge-graph-experience">
    <div class="container">
      <h3>知识图谱体验</h3>
      <p>探索知识点之间的关联关系</p>
      <div class="graph-demo-container">
        <InteractiveDemo />
      </div>
      <button @click="exploreMore" class="btn-explore">
        深入探索 →
      </button>
    </div>
  </section>
</template>
```

### 具体文件修改清单

#### 阶段一：首页重构文件
1. **src/pages/MainHomePage.vue**
   - 添加轮播图组件引用
   - 集成知识图谱体验区
   - 调整整体布局结构
   - 保留现有玻璃态效果

2. **src/components/home/<USER>
   - 实现轮播图功能
   - 参考原型图样式
   - 添加自动播放逻辑
   - 实现响应式布局

3. **src/components/home/<USER>
   - 集成InteractiveDemo组件
   - 添加引导文案
   - 实现跳转逻辑
   - 优化展示效果

4. **src/router/routes.js**
   - 移除`/knowledge-graph`路由
   - 调整相关重定向逻辑
   - 更新路由元信息

#### 阶段二：教师端重构文件
1. **src/layouts/TeacherLayout.vue**
   - 参考教师端原型图重构布局
   - 优化侧边栏导航
   - 改进头部用户信息展示
   - 添加快捷操作区域

2. **src/pages/teacher/TeacherDashboard.vue**（新建）
   - 创建教师端仪表板
   - 集成常用功能入口
   - 添加使用统计展示
   - 实现快捷操作

3. **src/components/teacher/TeacherSidebar.vue**（新建）
   - 独立的教师端侧边栏组件
   - 实现折叠/展开功能
   - 添加功能分组
   - 优化导航体验

#### 阶段三：学生端重构文件
1. **src/layouts/StudentLayout.vue**
   - 参考学生端原型图重构布局
   - 优化学习导航结构
   - 改进个人信息展示
   - 添加学习进度指示

2. **src/pages/student/StudentDashboard.vue**
   - 重构学习仪表板
   - 添加个性化推荐
   - 优化课程快捷入口
   - 集成学习统计

3. **src/components/student/StudentSidebar.vue**（新建）
   - 独立的学生端侧边栏组件
   - 实现学习路径导航
   - 添加进度指示器
   - 优化学习体验

### 样式文件修改计划

#### 1. 新增样式文件
- `src/assets/styles/carousel.css` - 轮播图专用样式
- `src/assets/styles/teacher-layout.css` - 教师端布局样式
- `src/assets/styles/student-layout.css` - 学生端布局样式

#### 2. 修改现有样式文件
- `src/assets/styles/glassmorphism.css` - 添加新的玻璃态效果
- `src/assets/styles/theme.css` - 扩展主题色彩
- `src/assets/css/global.css` - 添加全局布局类

### 数据流和状态管理

#### 1. 保持现有状态管理
- 用户认证状态（auth.js）
- 图谱数据管理（graph.js API）
- 课程数据管理（courses.js API）

#### 2. 新增状态管理需求
- 首页轮播图状态
- 用户偏好设置
- 页面导航状态

### 测试策略详细说明

#### 1. 功能测试检查清单
**知识图谱功能**：
- [ ] 图谱列表正常加载
- [ ] 图谱创建功能正常
- [ ] 图谱编辑功能正常
- [ ] 图谱删除功能正常
- [ ] 节点操作功能正常
- [ ] 图谱布局切换正常
- [ ] 图谱导入导出正常

**教师端功能**：
- [ ] 教案生成功能正常
- [ ] PPT生成功能正常
- [ ] 课程管理功能正常
- [ ] 资料管理功能正常
- [ ] 个人中心功能正常

**学生端功能**：
- [ ] 学习仪表板正常
- [ ] 课程学习功能正常
- [ ] 进度跟踪功能正常
- [ ] 资料访问功能正常
- [ ] 个人设置功能正常

#### 2. 兼容性测试
**浏览器支持**：
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

**设备适配**：
- 桌面端（1920x1080, 1366x768）
- 平板端（768x1024, 1024x768）
- 移动端（375x667, 414x896）

### 性能优化方案

#### 1. 代码分割
- 路由级别的懒加载
- 组件级别的异步加载
- 第三方库的按需引入

#### 2. 资源优化
- 图片压缩和格式优化
- CSS和JS文件压缩
- 字体文件优化

#### 3. 缓存策略
- API响应缓存
- 静态资源缓存
- 组件状态缓存

### 部署和发布计划

#### 1. 开发环境测试
- 本地开发环境验证
- 功能完整性测试
- 性能基准测试

#### 2. 预发布环境测试
- 完整功能测试
- 兼容性测试
- 压力测试

#### 3. 生产环境发布
- 灰度发布策略
- 监控和告警
- 快速回滚方案

---

**重要提醒**：
1. 每个阶段完成后必须进行充分测试，确保功能完整性
2. 保持与原型图的视觉一致性，但允许根据技术实现需要进行合理调整
3. 所有现有API接口和数据结构保持不变，确保后端兼容性
4. 重构过程中如发现问题，及时记录并制定解决方案
5. 建议使用Git分支管理，每个阶段创建独立分支进行开发
6. 定期备份代码，确保可以快速回滚到稳定版本

## 具体代码实现示例

### 1. 首页轮播图组件实现

**src/components/home/<USER>
```vue
<template>
  <section class="carousel-container">
    <div class="carousel-wrapper" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
      <div v-for="(slide, index) in slides" :key="index"
           class="carousel-slide" :class="`slide-${index + 1}`">
        <div class="slide-content">
          <h2>{{ slide.title }}</h2>
          <p>{{ slide.description }}</p>
          <div class="entry-buttons">
            <router-link :to="slide.teacherLink" class="btn-teacher">教师入口</router-link>
            <router-link :to="slide.studentLink" class="btn-student">学生入口</router-link>
          </div>
        </div>
      </div>
    </div>
    <button class="carousel-arrow prev" @click="prevSlide">‹</button>
    <button class="carousel-arrow next" @click="nextSlide">›</button>
    <div class="carousel-nav">
      <div v-for="(slide, index) in slides" :key="index"
           class="nav-dot" :class="{ active: index === currentSlide }"
           @click="goToSlide(index)"></div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const currentSlide = ref(0)
const autoPlayInterval = ref(null)

const slides = [
  {
    title: 'AI赋能OBE智慧教学平台',
    description: '基于成果导向教育理念，融合人工智能技术的新一代教学平台',
    teacherLink: '/teacher',
    studentLink: '/student'
  },
  {
    title: '智能化教学管理',
    description: 'AI助手帮助教师高效备课，个性化推荐教学资源，提升教学质量',
    teacherLink: '/teacher',
    studentLink: '/student'
  },
  {
    title: '个性化学习体验',
    description: '智能推荐学习路径，实时跟踪学习进度，让每个学生都能获得最适合的教育',
    teacherLink: '/teacher',
    studentLink: '/student'
  }
]

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % slides.length
}

const prevSlide = () => {
  currentSlide.value = (currentSlide.value - 1 + slides.length) % slides.length
}

const goToSlide = (index) => {
  currentSlide.value = index
}

const startAutoPlay = () => {
  autoPlayInterval.value = setInterval(nextSlide, 5000)
}

const stopAutoPlay = () => {
  if (autoPlayInterval.value) {
    clearInterval(autoPlayInterval.value)
    autoPlayInterval.value = null
  }
}

onMounted(() => {
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
})
</script>
```

### 2. 知识图谱体验区组件

**src/components/home/<USER>
```vue
<template>
  <section class="knowledge-graph-experience">
    <div class="container">
      <div class="section-header">
        <h3>知识图谱体验</h3>
        <p class="section-subtitle">探索知识点之间的关联关系，构建完整的知识体系</p>
      </div>

      <div class="experience-content">
        <div class="graph-demo-container">
          <div class="demo-controls">
            <button @click="resetDemo" class="control-btn">重置演示</button>
            <button @click="switchLayout" class="control-btn">切换布局</button>
            <button @click="addRandomNode" class="control-btn">添加节点</button>
          </div>

          <div class="graph-wrapper">
            <InteractiveDemo
              ref="demoRef"
              :demo-mode="true"
              @node-click="handleNodeClick"
            />
          </div>

          <div class="demo-info" v-if="selectedNode">
            <h4>{{ selectedNode.text }}</h4>
            <p>{{ selectedNode.description || '点击节点查看详细信息' }}</p>
          </div>
        </div>

        <div class="experience-actions">
          <button @click="exploreMore" class="btn-explore">
            深入探索知识图谱 →
          </button>
          <button @click="createGraph" class="btn-create">
            创建我的图谱 +
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import InteractiveDemo from './InteractiveDemo.vue'

const router = useRouter()
const demoRef = ref(null)
const selectedNode = ref(null)

const handleNodeClick = (node) => {
  selectedNode.value = node
}

const resetDemo = () => {
  if (demoRef.value) {
    demoRef.value.resetGraph()
  }
  selectedNode.value = null
}

const switchLayout = () => {
  if (demoRef.value) {
    demoRef.value.switchLayout()
  }
}

const addRandomNode = () => {
  if (demoRef.value) {
    demoRef.value.addRandomNode()
  }
}

const exploreMore = () => {
  // 跳转到图谱列表页面
  router.push('/my?tab=graphview')
}

const createGraph = () => {
  // 跳转到图谱创建页面
  router.push('/graph/create')
}
</script>
```

### 3. 路由配置修改

**src/router/routes.js**（修改后）：
```javascript
const routes = [
  {
    path: '/',
    name: 'home',
    component: () => import('@/pages/MainHomePage.vue'),
    meta: {
      title: 'HiProf 智能教育平台'
    }
  },
  // 移除独立的知识图谱首页路由
  // {
  //   path: '/knowledge-graph',
  //   name: 'knowledge-graph',
  //   component: () => import('@/pages/KnowledgeGraphHomePage.vue'),
  //   meta: {
  //     title: '知识图谱首页'
  //   }
  // },
  {
    path: '/ai-assistant',
    name: 'ai-assistant',
    component: () => import('@/pages/AIAssistantPage.vue'),
    meta: {
      title: 'AI智能助手'
    }
  },
  {
    path: '/teacher',
    name: 'teacher',
    component: () => import('@/layouts/TeacherLayout.vue'),
    meta: {
      title: '教师备课助手',
      requiresAuth: true,
      role: 'teacher'
    },
    children: [
      {
        path: '',
        redirect: '/teacher/dashboard'
      },
      {
        path: 'dashboard',
        name: 'teacher-dashboard',
        component: () => import('@/pages/teacher/TeacherDashboard.vue'),
        meta: {
          title: '教师工作台'
        }
      },
      // 保留现有的教师端子路由...
    ]
  },
  {
    path: '/student',
    name: 'student',
    component: () => import('@/layouts/StudentLayout.vue'),
    meta: {
      title: '学生学习中心',
      requiresAuth: true,
      role: 'student'
    },
    children: [
      {
        path: '',
        redirect: '/student/dashboard'
      },
      {
        path: 'dashboard',
        name: 'student-dashboard',
        component: () => import('@/pages/student/StudentDashboard.vue'),
        meta: {
          title: '学习仪表板'
        }
      },
      // 保留现有的学生端子路由...
    ]
  },
  // 保留图谱相关路由
  {
    path: '/graph',
    name: 'graph',
    component: () => import('@/pages/GraphPage.vue'),
    meta: {
      title: '知识图谱'
    }
  },
  {
    path: '/graph/:id',
    name: 'course-graph',
    component: () => import('@/pages/GraphPage.vue'),
    meta: {
      title: '课程知识图谱'
    }
  },
  // 其他路由保持不变...
]
```

### 4. 样式文件示例

**src/assets/styles/carousel.css**（新建）：
```css
.carousel-container {
  margin: 70px 100px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  height: 400px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.carousel-wrapper {
  display: flex;
  transition: transform 0.5s ease-in-out;
  height: 100%;
}

.carousel-slide {
  min-width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 70px;
  position: relative;
}

.slide-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.slide-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.slide-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.slide-content h2 {
  font-size: 36px;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.slide-content p {
  font-size: 18px;
  margin-bottom: 40px;
  opacity: 0.9;
}

.entry-buttons {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.btn-teacher, .btn-student {
  background: rgba(255,255,255,0.2);
  color: white;
  padding: 12px 30px;
  border-radius: 20px;
  text-decoration: none;
  font-size: 14px;
  border: 2px solid rgba(255,255,255,0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.btn-teacher:hover, .btn-student:hover {
  background: rgba(255,255,255,0.3);
  transform: translateY(-2px);
}

.carousel-nav {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255,255,255,0.5);
  cursor: pointer;
  transition: all 0.3s;
}

.nav-dot.active {
  background: white;
  transform: scale(1.2);
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255,255,255,0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
}

.carousel-arrow:hover {
  background: rgba(255,255,255,0.3);
}

.carousel-arrow.prev {
  left: 20px;
}

.carousel-arrow.next {
  right: 20px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .carousel-container {
    margin: 20px;
    height: 300px;
  }

  .carousel-slide {
    padding: 40px 20px;
  }

  .slide-content h2 {
    font-size: 24px;
  }

  .slide-content p {
    font-size: 14px;
  }

  .entry-buttons {
    flex-direction: column;
    gap: 15px;
  }
}
```

### 5. 知识图谱体验区样式

**src/assets/styles/knowledge-graph-experience.css**（新建）：
```css
.knowledge-graph-experience {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.knowledge-graph-experience .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h3 {
  font-size: 32px;
  color: #1e293b;
  margin-bottom: 20px;
  font-weight: 700;
}

.section-subtitle {
  font-size: 16px;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

.experience-content {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.graph-demo-container {
  margin-bottom: 40px;
}

.demo-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
}

.control-btn {
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #64748b;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.control-btn:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  color: #3b82f6;
}

.graph-wrapper {
  height: 400px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.demo-info {
  margin-top: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.demo-info h4 {
  font-size: 18px;
  color: #1e293b;
  margin-bottom: 8px;
}

.demo-info p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.experience-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.btn-explore, .btn-create {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.btn-explore {
  background: #3b82f6;
  color: white;
}

.btn-explore:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

.btn-create {
  background: transparent;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.btn-create:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .knowledge-graph-experience {
    padding: 40px 0;
  }

  .experience-content {
    padding: 20px;
  }

  .demo-controls {
    flex-wrap: wrap;
    gap: 10px;
  }

  .graph-wrapper {
    height: 300px;
  }

  .experience-actions {
    flex-direction: column;
    gap: 15px;
  }
}
```

## 实施时间表和里程碑

### 第一周：准备和分析阶段
**Day 1-2: 代码分析和规划**
- [ ] 完成现有代码结构深度分析
- [ ] 制定详细的组件迁移方案
- [ ] 确定API接口使用情况
- [ ] 创建开发分支 `feature/homepage-refactor`

**Day 3-4: 原型图分析和设计**
- [ ] 详细分析三个原型图设计要求
- [ ] 提取UI组件和布局规范
- [ ] 制定样式迁移计划
- [ ] 准备设计资源和图标

### 第二周：首页重构实施
**Day 5-6: 轮播图组件开发**
- [ ] 创建 `HomeCarousel.vue` 组件
- [ ] 实现轮播图基本功能
- [ ] 添加自动播放和手动控制
- [ ] 完成响应式适配

**Day 7-8: 知识图谱体验区集成**
- [ ] 创建 `KnowledgeGraphExperience.vue` 组件
- [ ] 集成 `InteractiveDemo` 组件
- [ ] 实现交互功能和跳转逻辑
- [ ] 优化展示效果

**Day 9-10: 首页整体重构**
- [ ] 重构 `MainHomePage.vue` 主组件
- [ ] 集成新开发的组件
- [ ] 调整布局和样式
- [ ] 测试功能完整性

### 第三周：教师端页面重构
**Day 11-12: 教师端布局重构**
- [ ] 重构 `TeacherLayout.vue` 布局组件
- [ ] 创建 `TeacherSidebar.vue` 侧边栏组件
- [ ] 优化头部和导航结构
- [ ] 实现响应式布局

**Day 13-14: 教师端功能整合**
- [ ] 创建 `TeacherDashboard.vue` 仪表板
- [ ] 整合知识图谱管理功能
- [ ] 优化教案和PPT生成界面
- [ ] 完善个人中心功能

### 第四周：学生端页面重构
**Day 15-16: 学生端布局重构**
- [ ] 重构 `StudentLayout.vue` 布局组件
- [ ] 创建 `StudentSidebar.vue` 侧边栏组件
- [ ] 优化学习导航结构
- [ ] 实现个性化布局

**Day 17-18: 学生端功能优化**
- [ ] 重构 `StudentDashboard.vue` 学习仪表板
- [ ] 优化知识图谱学习界面
- [ ] 改进课程学习功能
- [ ] 完善学习进度跟踪

### 第五周：测试和优化
**Day 19-20: 功能测试**
- [ ] 完整功能测试
- [ ] 兼容性测试
- [ ] 性能测试
- [ ] 用户体验测试

**Day 21: 最终优化和部署准备**
- [ ] 修复发现的问题
- [ ] 代码审查和优化
- [ ] 文档更新
- [ ] 部署准备

## 质量检查清单

### 功能完整性检查
- [ ] 所有现有功能在新页面中正常工作
- [ ] 知识图谱功能完整迁移
- [ ] 教师端所有功能正常
- [ ] 学生端所有功能正常
- [ ] 路由跳转和权限控制正常
- [ ] API接口调用正常
- [ ] 数据持久化正常

### 用户体验检查
- [ ] 页面加载速度符合要求
- [ ] 动画和过渡效果流畅
- [ ] 响应式布局效果良好
- [ ] 操作流程简化且直观
- [ ] 错误处理和提示完善
- [ ] 无障碍访问支持

### 技术质量检查
- [ ] 代码结构清晰，符合规范
- [ ] 组件复用率高，冗余代码少
- [ ] 样式组织合理，易于维护
- [ ] 性能优化措施到位
- [ ] 安全措施完善
- [ ] 文档和注释完整

### 兼容性检查
- [ ] Chrome 90+ 正常运行
- [ ] Firefox 88+ 正常运行
- [ ] Safari 14+ 正常运行
- [ ] Edge 90+ 正常运行
- [ ] 移动端浏览器正常运行
- [ ] 不同屏幕尺寸适配良好

## 风险应对预案

### 技术风险应对
1. **组件迁移失败**
   - 备份原有组件
   - 分步骤迁移，逐个验证
   - 保留回滚方案

2. **性能下降**
   - 实施代码分割
   - 优化图片和资源
   - 使用缓存策略

3. **兼容性问题**
   - 使用Polyfill处理
   - 渐进式增强
   - 降级方案准备

### 进度风险应对
1. **开发时间超期**
   - 优先保证核心功能
   - 分阶段发布
   - 调整功能范围

2. **测试时间不足**
   - 并行开发和测试
   - 自动化测试
   - 重点功能优先

### 质量风险应对
1. **功能缺失**
   - 详细功能清单检查
   - 用户验收测试
   - 快速修复机制

2. **用户体验下降**
   - 用户反馈收集
   - A/B测试验证
   - 持续优化改进

## 成功交付标准

### 必须达成的目标
1. **功能完整性**: 100%现有功能正常工作
2. **视觉一致性**: 95%符合原型图设计要求
3. **性能标准**: 页面加载时间不超过3秒
4. **兼容性**: 支持主流浏览器和设备
5. **代码质量**: 通过代码审查和质量检测

### 期望达成的目标
1. **用户体验**: 操作流程简化20%以上
2. **维护性**: 代码复用率提升30%以上
3. **扩展性**: 支持未来功能扩展
4. **性能优化**: 首屏加载时间优化15%以上

## 后续维护和支持

### 短期支持（交付后1个月）
- 7x24小时技术支持
- 紧急问题4小时内响应
- 用户反馈收集和处理
- 小版本更新和修复

### 长期维护（交付后3个月）
- 定期性能监控和优化
- 功能增强和扩展
- 安全更新和补丁
- 用户培训和文档更新

---

**项目总结**：
本重构计划旨在将现有的HiProf前端页面重构为更加现代化、用户友好的三页面结构，同时保持所有现有功能的完整性。通过详细的分析、规划和实施，确保重构过程平稳进行，最终交付高质量的产品。

**关键成功因素**：
1. 详细的前期分析和规划
2. 分阶段实施和持续测试
3. 严格的质量控制和检查
4. 完善的风险应对机制
5. 持续的沟通和反馈

**预期收益**：
1. 提升用户体验和操作效率
2. 改善代码结构和可维护性
3. 增强平台的专业性和现代感
4. 为未来功能扩展奠定基础
