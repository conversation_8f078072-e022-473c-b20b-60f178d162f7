<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI赋能OBE智慧教学平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logo {
            width: 80px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            letter-spacing: 1px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: logoShine 3s infinite;
        }

        @keyframes logoShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }
        
        .platform-info h1 {
            font-size: 18px;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .platform-info p {
            font-size: 14px;
            color: #64748b;
        }
        
        .auth-buttons {
            display: flex;
            gap: 20px;
        }
        
        .btn {
            padding: 8px 20px;
            border-radius: 15px;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
            border: none;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-secondary {
            background: transparent;
            color: #3b82f6;
            border: 1px solid #3b82f6;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .carousel-container {
            margin: 70px 100px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            height: 400px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .carousel-wrapper {
            display: flex;
            transition: transform 0.5s ease-in-out;
            height: 100%;
        }

        .carousel-slide {
            min-width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 70px;
            position: relative;
        }

        .slide-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .slide-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .slide-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .slide-content h2 {
            font-size: 36px;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .slide-content p {
            font-size: 18px;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .entry-buttons {
            display: flex;
            justify-content: center;
            gap: 30px;
        }

        .btn-teacher {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 12px 30px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s;
        }

        .btn-teacher:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .btn-student {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 12px 30px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s;
        }

        .btn-student:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .carousel-nav {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: all 0.3s;
        }

        .nav-dot.active {
            background: white;
            transform: scale(1.2);
        }

        .carousel-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
            backdrop-filter: blur(10px);
        }

        .carousel-arrow:hover {
            background: rgba(255,255,255,0.3);
        }

        .carousel-arrow.prev {
            left: 20px;
        }

        .carousel-arrow.next {
            right: 20px;
        }
        
        .features-section {
            text-align: center;
            margin: 80px 0;
            padding: 0 50px;
        }

        .features-section h3 {
            font-size: 32px;
            color: #1e293b;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .features-subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .feature-card {
            background: white;
            border: none;
            border-radius: 16px;
            padding: 40px 30px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-card.ai {
            --card-color: #3b82f6;
            --card-color-light: #60a5fa;
        }

        .feature-card.obe {
            --card-color: #10b981;
            --card-color-light: #34d399;
        }

        .feature-card.smart {
            --card-color: #f59e0b;
            --card-color-light: #fbbf24;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: rotate(45deg);
            animation: iconShine 2s infinite;
        }

        @keyframes iconShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .feature-icon.ai {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }
        .feature-icon.obe {
            background: linear-gradient(135deg, #10b981, #047857);
        }
        .feature-icon.smart {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .feature-card h4 {
            font-size: 20px;
            color: #1e293b;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .feature-card p {
            font-size: 14px;
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .feature-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f1f5f9;
        }

        .feature-stat {
            text-align: center;
        }

        .feature-stat-number {
            font-size: 18px;
            font-weight: 700;
            color: var(--card-color);
            display: block;
        }

        .feature-stat-label {
            font-size: 12px;
            color: #94a3b8;
            margin-top: 2px;
        }
        
        .footer {
            background: #1e293b;
            color: #94a3b8;
            text-align: center;
            padding: 30px;
        }
        
        .footer p {
            margin-bottom: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo-section">
            <div class="logo">HiProf</div>
            <div class="platform-info">
                <h1>AI赋能OBE智慧教学平台</h1>
                <p>Intelligent Teaching Platform Powered by AI</p>
            </div>
        </div>
        <div class="auth-buttons">
            <a href="#" class="btn btn-primary">登录</a>
            <a href="#" class="btn btn-secondary">注册</a>
        </div>
    </header>

    <main>
        <section class="carousel-container">
            <div class="carousel-wrapper" id="carouselWrapper">
                <div class="carousel-slide slide-1">
                    <div class="slide-content">
                        <h2>AI赋能OBE智慧教学平台</h2>
                        <p>基于成果导向教育理念，融合人工智能技术的新一代教学平台</p>
                        <div class="entry-buttons">
                            <a href="教师端个人中心.html" class="btn-teacher">教师入口</a>
                            <a href="学生端个人中心.html" class="btn-student">学生入口</a>
                        </div>
                    </div>
                </div>
                <div class="carousel-slide slide-2">
                    <div class="slide-content">
                        <h2>智能化教学管理</h2>
                        <p>AI助手帮助教师高效备课，个性化推荐教学资源，提升教学质量</p>
                        <div class="entry-buttons">
                            <a href="教师端个人中心.html" class="btn-teacher">开始体验</a>
                            <a href="#" class="btn-student">了解更多</a>
                        </div>
                    </div>
                </div>
                <div class="carousel-slide slide-3">
                    <div class="slide-content">
                        <h2>个性化学习体验</h2>
                        <p>智能推荐学习路径，实时跟踪学习进度，让每个学生都能获得最适合的教育</p>
                        <div class="entry-buttons">
                            <a href="学生端个人中心.html" class="btn-student">立即学习</a>
                            <a href="#" class="btn-teacher">查看案例</a>
                        </div>
                    </div>
                </div>
            </div>
            <button class="carousel-arrow prev" onclick="prevSlide()">‹</button>
            <button class="carousel-arrow next" onclick="nextSlide()">›</button>
            <div class="carousel-nav">
                <div class="nav-dot active" onclick="goToSlide(0)"></div>
                <div class="nav-dot" onclick="goToSlide(1)"></div>
                <div class="nav-dot" onclick="goToSlide(2)"></div>
            </div>
        </section>

        <section class="features-section">
            <h3>平台特色</h3>
            <p class="features-subtitle">融合前沿技术与教育理念，为师生提供全方位的智慧教学解决方案</p>
            <div class="features-grid">
                <div class="feature-card ai">
                    <div class="feature-icon ai">AI</div>
                    <h4>智能助手</h4>
                    <p>AI驱动的个性化教学辅助系统，智能推荐教学资源，自动生成教案，提供实时答疑支持</p>
                    <div class="feature-stats">
                        <div class="feature-stat">
                            <span class="feature-stat-number">95%</span>
                            <span class="feature-stat-label">准确率</span>
                        </div>
                        <div class="feature-stat">
                            <span class="feature-stat-number">24/7</span>
                            <span class="feature-stat-label">在线服务</span>
                        </div>
                    </div>
                </div>
                <div class="feature-card obe">
                    <div class="feature-icon obe">OBE</div>
                    <h4>成果导向</h4>
                    <p>基于学习成果的教育理念设计，明确学习目标，量化评估标准，确保教学质量和学习效果</p>
                    <div class="feature-stats">
                        <div class="feature-stat">
                            <span class="feature-stat-number">100+</span>
                            <span class="feature-stat-label">评估指标</span>
                        </div>
                        <div class="feature-stat">
                            <span class="feature-stat-number">实时</span>
                            <span class="feature-stat-label">反馈</span>
                        </div>
                    </div>
                </div>
                <div class="feature-card smart">
                    <div class="feature-icon smart">智慧</div>
                    <h4>智慧教学</h4>
                    <p>数据驱动的教学决策支持系统，深度分析学习行为，优化教学策略，提升教育质量</p>
                    <div class="feature-stats">
                        <div class="feature-stat">
                            <span class="feature-stat-number">TB级</span>
                            <span class="feature-stat-label">数据分析</span>
                        </div>
                        <div class="feature-stat">
                            <span class="feature-stat-number">秒级</span>
                            <span class="feature-stat-label">响应速度</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <p>© 2025 AI赋能OBE智慧教学平台 HiProf版权所有</p>
        <p>联系我们 | 帮助中心 | 隐私政策 | 法律声明</p>
    </footer>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-slide');
        const dots = document.querySelectorAll('.nav-dot');
        const wrapper = document.getElementById('carouselWrapper');

        function updateSlide() {
            wrapper.style.transform = `translateX(-${currentSlide * 100}%)`;

            // 更新导航点
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            updateSlide();
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            updateSlide();
        }

        function goToSlide(index) {
            currentSlide = index;
            updateSlide();
        }

        // 自动轮播
        setInterval(nextSlide, 5000);

        // 添加按钮点击效果
        document.querySelectorAll('.btn, .btn-teacher, .btn-student').forEach(btn => {
            btn.addEventListener('click', function(e) {
                // 如果是链接到其他页面，允许默认行为
                if (this.getAttribute('href') && this.getAttribute('href') !== '#') {
                    return;
                }
                e.preventDefault();
                console.log('按钮被点击:', this.textContent);
            });
        });

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                prevSlide();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
            }
        });
    </script>
</body>
</html>
