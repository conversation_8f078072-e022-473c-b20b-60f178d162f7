<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师端个人中心 - AI赋能OBE智慧教学平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logo {
            width: 70px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            letter-spacing: 1px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .platform-info h1 {
            font-size: 16px;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .platform-info p {
            font-size: 12px;
            color: #64748b;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 12px;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e2e8f0;
        }
        
        .sidebar-header {
            background: #f8fafc;
            padding: 20px 30px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .sidebar-header h2 {
            font-size: 16px;
            color: #1e293b;
        }
        
        .menu-list {
            padding: 20px;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px 15px;
            margin-bottom: 15px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            color: #64748b;
        }
        
        .menu-item.active {
            background: #3b82f6;
            color: white;
        }
        
        .menu-item:hover:not(.active) {
            background: #f1f5f9;
        }
        
        .menu-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }
        
        .menu-icon.knowledge { background: #3b82f6; }
        .menu-icon.course { background: #10b981; }
        .menu-icon.prepare { background: #f59e0b; }
        .menu-icon.student { background: #8b5cf6; }
        .menu-icon.homework { background: #ec4899; }
        .menu-icon.ai { background: #ef4444; }
        .menu-icon.library { background: #06b6d4; }
        .menu-icon.settings { background: #64748b; }
        
        .content-area {
            flex: 1;
            background: white;
            overflow-y: auto;
        }
        
        .content-header {
            background: #f8fafc;
            padding: 20px 30px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .content-header h3 {
            font-size: 20px;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .content-header p {
            font-size: 14px;
            color: #64748b;
        }
        
        .content-body {
            padding: 30px;
        }
        
        .action-buttons {
            display: flex;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .btn {
            padding: 8px 24px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-secondary {
            background: transparent;
            color: #3b82f6;
            border: 1px solid #3b82f6;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .knowledge-graph-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }

        .graph-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .graph-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .graph-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .graph-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .graph-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .graph-icon.civil { background: #3b82f6; }
        .graph-icon.fem { background: #10b981; }
        .graph-icon.engineering { background: #f59e0b; }

        .graph-info h4 {
            font-size: 16px;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .graph-info p {
            font-size: 12px;
            color: #64748b;
        }

        .graph-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 12px;
            color: #64748b;
        }

        .graph-actions {
            display: flex;
            gap: 10px;
        }

        .graph-btn {
            padding: 6px 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .graph-btn.primary {
            background: #3b82f6;
            color: white;
        }

        .graph-btn.secondary {
            background: transparent;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }

        .graph-btn:hover {
            transform: translateY(-1px);
        }

        .graph-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }

        .graph-status.active {
            background: #dcfce7;
            color: #166534;
        }

        .graph-status.draft {
            background: #fef3c7;
            color: #92400e;
        }

        .toolbar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
        }

        .toolbar-btn {
            padding: 8px 16px;
            background: #f1f5f9;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            color: #64748b;
            cursor: pointer;
            transition: all 0.3s;
        }

        .toolbar-btn:hover {
            background: #e2e8f0;
        }

        .search-box {
            flex: 1;
            max-width: 300px;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
        }

        .search-box:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .page-content {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .course-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .course-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s;
        }

        .course-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .course-header {
            padding: 20px;
            color: white;
        }

        .course-header h4 {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .course-header p {
            font-size: 12px;
            opacity: 0.9;
        }

        .course-body {
            padding: 20px;
        }

        .course-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 12px;
            color: #64748b;
        }

        .course-progress {
            margin-bottom: 15px;
        }

        .course-progress span {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 5px;
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s;
        }

        .course-actions {
            display: flex;
            gap: 10px;
        }

        .prepare-tools {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }

        .tool-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s;
        }

        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .tool-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .tool-card h4 {
            font-size: 16px;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .tool-card p {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 15px;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }

        .file-item {
            transition: all 0.3s;
            cursor: pointer;
        }

        .file-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .ai-tool-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .ai-tool-card .tool-icon {
            position: relative;
            overflow: hidden;
        }

        .ai-tool-card .tool-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: rotate(45deg);
            animation: iconShine 3s infinite;
        }

        .homework-card:hover,
        .discussion-item:hover,
        .homework-module-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .homework-module-card .module-icon {
            position: relative;
            overflow: hidden;
        }

        .homework-module-card .module-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: rotate(45deg);
            animation: iconShine 3s infinite;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo-section">
            <div class="logo">HiProf</div>
            <div class="platform-info">
                <h1>AI赋能OBE智慧教学平台</h1>
                <p>教师工作台</p>
            </div>
        </div>
        <div class="user-info">
            <div class="user-avatar">李老师</div>
        </div>
    </header>

    <div class="main-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>个人中心</h2>
            </div>
            <nav class="menu-list">
                <a href="#" class="menu-item active" data-content="knowledge">
                    <div class="menu-icon knowledge">图</div>
                    <span>知识图谱</span>
                </a>
                <a href="#" class="menu-item" data-content="course">
                    <div class="menu-icon course">课</div>
                    <span>课程</span>
                </a>
                <a href="#" class="menu-item" data-content="prepare">
                    <div class="menu-icon prepare">备</div>
                    <span>备课助手</span>
                </a>
                <a href="#" class="menu-item" data-content="student">
                    <div class="menu-icon student">学</div>
                    <span>学生管理</span>
                </a>
                <a href="#" class="menu-item" data-content="homework">
                    <div class="menu-icon homework">作</div>
                    <span>学生作业与讨论管理</span>
                </a>
                <a href="#" class="menu-item" data-content="ai">
                    <div class="menu-icon ai">AI</div>
                    <span>AI助手</span>
                </a>
                <a href="#" class="menu-item" data-content="library">
                    <div class="menu-icon library">库</div>
                    <span>资料库</span>
                </a>
                <a href="#" class="menu-item" data-content="settings">
                    <div class="menu-icon settings">设</div>
                    <span>设置</span>
                </a>
            </nav>
        </aside>

        <main class="content-area">
            <div class="content-header">
                <h3 id="content-title">知识图谱</h3>
                <p id="content-description">构建和管理课程知识体系</p>
            </div>
            <div class="content-body">
                <!-- 知识图谱页面 -->
                <div id="knowledge-content" class="page-content">
                    <div class="action-buttons">
                        <button class="btn btn-primary">新建图谱</button>
                        <button class="btn btn-secondary">导入图谱</button>
                    </div>

                    <div class="toolbar">
                        <input type="text" class="search-box" placeholder="搜索知识图谱...">
                        <button class="toolbar-btn">全部</button>
                        <button class="toolbar-btn">已发布</button>
                        <button class="toolbar-btn">草稿</button>
                        <button class="toolbar-btn">排序</button>
                    </div>

                    <div class="knowledge-graph-container">
                        <div class="graph-list">
                            <div class="graph-item">
                                <div class="graph-status active">已发布</div>
                                <div class="graph-header">
                                    <div class="graph-icon civil">土木</div>
                                    <div class="graph-info">
                                        <h4>土木工程概论</h4>
                                        <p>土木工程基础知识体系</p>
                                    </div>
                                </div>
                                <div class="graph-stats">
                                    <span>节点数: 45</span>
                                    <span>关系数: 68</span>
                                    <span>更新时间: 2024-12-15</span>
                                </div>
                                <div class="graph-actions">
                                    <button class="graph-btn primary">编辑</button>
                                    <button class="graph-btn secondary">预览</button>
                                    <button class="graph-btn secondary">分享</button>
                                </div>
                            </div>

                            <div class="graph-item">
                                <div class="graph-status active">已发布</div>
                                <div class="graph-header">
                                    <div class="graph-icon fem">有限</div>
                                    <div class="graph-info">
                                        <h4>有限元</h4>
                                        <p>有限元分析方法与应用</p>
                                    </div>
                                </div>
                                <div class="graph-stats">
                                    <span>节点数: 32</span>
                                    <span>关系数: 41</span>
                                    <span>更新时间: 2024-12-10</span>
                                </div>
                                <div class="graph-actions">
                                    <button class="graph-btn primary">编辑</button>
                                    <button class="graph-btn secondary">预览</button>
                                    <button class="graph-btn secondary">分享</button>
                                </div>
                            </div>

                            <div class="graph-item">
                                <div class="graph-status draft">草稿</div>
                                <div class="graph-header">
                                    <div class="graph-icon engineering">工程</div>
                                    <div class="graph-info">
                                        <h4>土木工程</h4>
                                        <p>土木工程专业知识图谱</p>
                                    </div>
                                </div>
                                <div class="graph-stats">
                                    <span>节点数: 28</span>
                                    <span>关系数: 35</span>
                                    <span>更新时间: 2024-12-08</span>
                                </div>
                                <div class="graph-actions">
                                    <button class="graph-btn primary">继续编辑</button>
                                    <button class="graph-btn secondary">预览</button>
                                    <button class="graph-btn secondary">删除</button>
                                </div>
                            </div>

                            <div class="graph-item" style="border: 2px dashed #e2e8f0; display: flex; align-items: center; justify-content: center; min-height: 200px;">
                                <div style="text-align: center; color: #64748b;">
                                    <div style="font-size: 24px; margin-bottom: 10px;">+</div>
                                    <div>创建新的知识图谱</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 课程页面 -->
                <div id="course-content" class="page-content" style="display: none;">
                    <div class="action-buttons">
                        <button class="btn btn-primary">新建课程</button>
                        <button class="btn btn-secondary">导入课程</button>
                    </div>

                    <div class="course-grid">
                        <div class="course-card">
                            <div class="course-header" style="background: #3b82f6;">
                                <h4>土木工程概论</h4>
                                <p>2024春季学期</p>
                            </div>
                            <div class="course-body">
                                <div class="course-info">
                                    <span>学生人数: 45人</span>
                                    <span>课时: 48学时</span>
                                </div>
                                <div class="course-progress">
                                    <span>进度: 75%</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 75%; background: #3b82f6;"></div>
                                    </div>
                                </div>
                                <div class="course-actions">
                                    <button class="btn btn-primary">进入课程</button>
                                    <button class="btn btn-secondary">编辑</button>
                                </div>
                            </div>
                        </div>

                        <div class="course-card">
                            <div class="course-header" style="background: #10b981;">
                                <h4>有限元分析</h4>
                                <p>2024春季学期</p>
                            </div>
                            <div class="course-body">
                                <div class="course-info">
                                    <span>学生人数: 32人</span>
                                    <span>课时: 36学时</span>
                                </div>
                                <div class="course-progress">
                                    <span>进度: 60%</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 60%; background: #10b981;"></div>
                                    </div>
                                </div>
                                <div class="course-actions">
                                    <button class="btn btn-primary">进入课程</button>
                                    <button class="btn btn-secondary">编辑</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 备课助手页面 -->
                <div id="prepare-content" class="page-content" style="display: none;">
                    <div class="action-buttons">
                        <button class="btn btn-primary">新建教案</button>
                        <button class="btn btn-secondary">AI生成教案</button>
                    </div>

                    <div class="prepare-tools">
                        <div class="tool-card">
                            <div class="tool-icon" style="background: #3b82f6;">📝</div>
                            <h4>教案设计</h4>
                            <p>智能教案生成和编辑工具</p>
                            <button class="btn btn-primary">开始设计</button>
                        </div>

                        <div class="tool-card">
                            <div class="tool-icon" style="background: #8b5cf6;">📋</div>
                            <h4>我的教案</h4>
                            <p>查看和管理已创建的教案</p>
                            <button class="btn btn-primary">查看教案</button>
                        </div>

                        <div class="tool-card">
                            <div class="tool-icon" style="background: #10b981;">📊</div>
                            <h4>课件制作</h4>
                            <p>多媒体课件制作和管理</p>
                            <button class="btn btn-primary">制作课件</button>
                        </div>

                        <div class="tool-card">
                            <div class="tool-icon" style="background: #f59e0b;">🎯</div>
                            <h4>习题库</h4>
                            <p>智能习题推荐和组卷</p>
                            <button class="btn btn-primary">题库管理</button>
                        </div>
                    </div>
                </div>

                <!-- 学生管理页面 -->
                <div id="student-content" class="page-content" style="display: none;">
                    <div class="action-buttons">
                        <button class="btn btn-primary">添加学生</button>
                        <button class="btn btn-secondary">批量导入</button>
                    </div>

                    <div class="student-table">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8fafc;">
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0;">学号</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0;">姓名</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0;">班级</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0;">学习进度</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">2024001</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">张三</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">土木1班</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">85%</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">
                                        <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">2024002</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">李四</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">土木1班</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">92%</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">
                                        <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 学生作业与讨论管理页面 -->
                <div id="homework-content" class="page-content" style="display: none;">
                    <div class="homework-modules-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 30px;">
                        <!-- 作业管理模块 -->
                        <div class="homework-module-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="module-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">📝</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">作业管理</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">发布作业、批改作业、查看提交进度和成绩统计</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px;">管理作业</button>
                        </div>

                        <!-- 讨论管理模块 -->
                        <div class="homework-module-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="module-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #10b981, #047857); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">💬</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">讨论管理</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">创建讨论话题、管理学生回复、维护讨论秩序</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px; background: #10b981;">管理讨论</button>
                        </div>

                        <!-- 在线答疑模块 -->
                        <div class="homework-module-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="module-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">❓</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">在线答疑</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">实时回答学生问题、提供学习指导和答疑服务</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px; background: #f59e0b;">开始答疑</button>
                        </div>

                        <!-- 学习分析模块 -->
                        <div class="homework-module-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="module-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">📊</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">学习分析</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">分析学生学习数据、生成学习报告和改进建议</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px; background: #8b5cf6;">查看分析</button>
                        </div>

                        <!-- 互动评价模块 -->
                        <div class="homework-module-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="module-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #ec4899, #db2777); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">⭐</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">互动评价</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">课堂互动评价、学生表现记录和综合评估</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px; background: #ec4899;">开始评价</button>
                        </div>

                        <!-- 资源共享模块 -->
                        <div class="homework-module-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="module-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #06b6d4, #0891b2); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">�</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">资源共享</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">分享学习资料、课件下载和参考资源管理</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px; background: #06b6d4;">管理资源</button>
                        </div>
                    </div>
                </div>

                <!-- AI助手页面 -->
                <div id="ai-content" class="page-content" style="display: none;">
                    <div class="ai-tools-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 30px;">
                        <!-- AI生成教案工具卡片 -->
                        <div class="ai-tool-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="tool-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">📝</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">AI生成教案</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">智能生成个性化教案，支持多种教学模板和课程类型</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px;">开始生成</button>
                        </div>

                        <!-- AI生成PPT工具卡片 -->
                        <div class="ai-tool-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="tool-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #10b981, #047857); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">📊</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">AI生成PPT</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">自动生成精美课件，支持多种设计风格和模板选择</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px; background: #10b981;">开始制作</button>
                        </div>

                        <!-- AI智能回答助手工具卡片 -->
                        <div class="ai-tool-card" style="background: white; border: 1px solid #e2e8f0; border-radius: 16px; padding: 30px; text-align: center; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div class="tool-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 20px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">🤖</div>
                            <h4 style="font-size: 18px; color: #1e293b; margin-bottom: 12px; font-weight: 600;">AI智能回答助手</h4>
                            <p style="font-size: 14px; color: #64748b; margin-bottom: 25px; line-height: 1.5;">专业教学问题解答，提供教学建议和资源推荐</p>
                            <button class="btn btn-primary" style="width: 100%; padding: 12px 24px; font-size: 14px; border-radius: 8px; background: #f59e0b;">开始对话</button>
                        </div>
                    </div>
                </div>

                <!-- 资料库页面 -->
                <div id="library-content" class="page-content" style="display: none;">
                    <div class="action-buttons">
                        <button class="btn btn-primary">上传资料</button>
                        <button class="btn btn-secondary">新建文件夹</button>
                    </div>

                    <div class="file-grid">
                        <div class="file-item" style="background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; text-align: center;">
                            <div style="font-size: 48px; color: #3b82f6; margin-bottom: 10px;">📁</div>
                            <h4>课程资料</h4>
                            <p style="color: #64748b; font-size: 12px;">25个文件</p>
                        </div>

                        <div class="file-item" style="background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; text-align: center;">
                            <div style="font-size: 48px; color: #10b981; margin-bottom: 10px;">📄</div>
                            <h4>教案模板</h4>
                            <p style="color: #64748b; font-size: 12px;">12个文件</p>
                        </div>

                        <div class="file-item" style="background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; text-align: center;">
                            <div style="font-size: 48px; color: #f59e0b; margin-bottom: 10px;">🎥</div>
                            <h4>视频资源</h4>
                            <p style="color: #64748b; font-size: 12px;">8个文件</p>
                        </div>
                    </div>
                </div>

                <!-- 设置页面 -->
                <div id="settings-content" class="page-content" style="display: none;">
                    <div class="settings-sections">
                        <div class="settings-section" style="background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                            <h4 style="margin-bottom: 15px;">个人信息</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; color: #64748b;">姓名</label>
                                    <input type="text" value="李老师" style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; color: #64748b;">邮箱</label>
                                    <input type="email" value="<EMAIL>" style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px;">
                                </div>
                            </div>
                        </div>

                        <div class="settings-section" style="background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                            <h4 style="margin-bottom: 15px;">通知设置</h4>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span>邮件通知</span>
                                <input type="checkbox" checked>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span>课程提醒</span>
                                <input type="checkbox" checked>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-primary">保存设置</button>
                            <button class="btn btn-secondary">重置</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 菜单切换功能
        const menuItems = document.querySelectorAll('.menu-item');
        const contentTitle = document.getElementById('content-title');
        const contentDescription = document.getElementById('content-description');

        const contentData = {
            knowledge: { title: '知识图谱', description: '构建和管理课程知识体系' },
            course: { title: '课程', description: '管理和组织教学课程' },
            prepare: { title: '备课助手', description: 'AI辅助备课和教案设计' },
            student: { title: '学生管理', description: '学生信息和学习进度管理' },
            homework: { title: '学生作业与讨论管理', description: '管理学生作业批改和课堂讨论' },
            ai: { title: 'AI助手', description: '智能教学辅助工具' },
            library: { title: '资料库', description: '教学资源存储和管理' },
            settings: { title: '设置', description: '个人偏好和系统配置' }
        };

        function showPageContent(contentType) {
            // 隐藏所有页面内容
            document.querySelectorAll('.page-content').forEach(content => {
                content.style.display = 'none';
            });

            // 显示选中的页面内容
            const targetContent = document.getElementById(contentType + '-content');
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        }

        menuItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // 移除所有活动状态
                menuItems.forEach(mi => mi.classList.remove('active'));

                // 添加当前活动状态
                this.classList.add('active');

                // 更新内容
                const contentType = this.getAttribute('data-content');
                const data = contentData[contentType];
                contentTitle.textContent = data.title;
                contentDescription.textContent = data.description;

                // 显示对应的页面内容
                showPageContent(contentType);
            });
        });

        // 搜索功能
        const searchBox = document.querySelector('.search-box');
        const graphItems = document.querySelectorAll('.graph-item');

        searchBox.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            graphItems.forEach(item => {
                const title = item.querySelector('h4');
                if (title && title.textContent.toLowerCase().includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 筛选功能
        const filterBtns = document.querySelectorAll('.toolbar-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有活动状态
                filterBtns.forEach(b => b.style.background = '#f1f5f9');
                // 添加当前活动状态
                this.style.background = '#3b82f6';
                this.style.color = 'white';

                const filter = this.textContent;
                graphItems.forEach(item => {
                    const status = item.querySelector('.graph-status');
                    if (filter === '全部') {
                        item.style.display = 'block';
                    } else if (filter === '已发布' && status && status.textContent === '已发布') {
                        item.style.display = 'block';
                    } else if (filter === '草稿' && status && status.textContent === '草稿') {
                        item.style.display = 'block';
                    } else if (filter !== '排序') {
                        item.style.display = 'none';
                    }
                });

                console.log('筛选:', filter);
            });
        });

        // 按钮点击效果
        document.querySelectorAll('.btn, .graph-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log('按钮被点击:', this.textContent);
            });
        });

        // 知识图谱卡片点击效果
        document.querySelectorAll('.graph-item').forEach(item => {
            item.addEventListener('click', function(e) {
                // 如果点击的是按钮，不触发卡片点击
                if (e.target.classList.contains('graph-btn')) {
                    return;
                }
                console.log('知识图谱被点击:', this.querySelector('h4').textContent);
            });
        });
    </script>
</body>
</html>
